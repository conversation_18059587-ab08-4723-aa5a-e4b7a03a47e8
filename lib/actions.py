import importlib
import importlib.util
from pathlib import Path

ACTIONS_DIR = Path.cwd() / "actions"


# First load function
def load_actions() -> None:
    """Load all actions from the actions directory."""
    if not ACTIONS_DIR.exists():
        msg = f"Actions directory not found: {ACTIONS_DIR}"
        raise FileNotFoundError(msg)

    for action_file in ACTIONS_DIR.glob("*.py"):
        action_name = action_file.stem
        spec = importlib.util.spec_from_file_location(action_name, action_file)
        if spec is None:
            raise ImportError(f"Could not load module from {action_file}")
        mod = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(mod)
        mod.test()
        print(f"Loaded {action_name}")
