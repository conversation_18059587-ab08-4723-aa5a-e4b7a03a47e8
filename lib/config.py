"""Configuration management for BoopBoard using Pydantic Settings."""

from pathlib import Path
from typing import Annotated

from pydantic import BaseModel, Field
from pydantic_settings import (
    BaseSettings,
    JsonConfigSettingsSource,
    PydanticBaseSettingsSource,
)


class Button(BaseModel):
    """Represents an action that a button can perform."""

    label: Annotated[str, Field(...)]
    type: Annotated[str, Field(...)]
    command: Annotated[str, Field(...)]

    # TODO(glitchy): Create method that will return the proper nicegui code


class Layer(BaseModel):
    """Represents a layer of buttons."""

    name: str
    icon: str
    buttons: list[Button]


class Config(BaseSettings):
    """Main configuration using Pydantic Settings."""

    layers: Annotated[dict[str, Layer], Field(...)]

    @classmethod
    def settings_customise_sources(
        cls,
        settings_cls: type[BaseSettings],
        init_settings: PydanticBaseSettingsSource,  # noqa: ARG003
        env_settings: PydanticBaseSettingsSource,  # noqa: ARG003
        dotenv_settings: PydanticBaseSettingsSource,  # noqa: ARG003
        file_secret_settings: PydanticBaseSettingsSource,  # noqa: ARG003
    ) -> tuple[PydanticBaseSettingsSource, ...]:
        """Customize settings sources to load from JSON file."""
        return (JsonConfigSettingsSource(settings_cls, Path("config.json")),)


if __name__ == "__main__":
    # Test
    print(Config().model_dump())  # noqa: T201
